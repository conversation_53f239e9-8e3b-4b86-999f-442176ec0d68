<template>
  <div
    class="contracts"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
      "
      label-position="right"
      label-width="110px"
    >
      <div style="display: flex; gap: 20px">
        <el-form-item label="文件名称" style="margin-bottom: 0">
          <el-input
            v-model="conditions.filters.protocolName"
            placeholder="请输入文件名称"
            style="width: 280px"
          ></el-input>
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="goSignatures">
        <i class="el-icon-plus" />
        发起签约
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="tableData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="protocolNo"
        label="合同编号"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="protocolName"
        label="合同名称"
        width="250"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="laborName"
        label="姓名"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="laborIdCard"
        label="身份证号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="corporationName"
        label="作业主体"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="startDate"
        label="合同开始时间"
        width="180"
        :formatter="formatDate"
      ></el-table-column>
      <el-table-column
        prop="endDate"
        label="合同结束时间"
        width="180"
        :formatter="formatDate"
      ></el-table-column>
      <el-table-column label="签署状态" width="120">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.protocolStatus)" size="small">
            {{ getStatusText(scope.row.protocolStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="150">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleDownload(scope.row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'Contracts',
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: false,
        filters: {
          protocolName: ''
        }
      },
      total: 0,
      tableData: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    goSignatures() {
      this.$router.push('/signaturesNew')
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions.filters.protocolName = ''
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.supplierProtocolList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDate(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleDateString('zh-CN')
    },

    getStatusType(status) {
      const statusMap = {
        PENDING_SIGN: 'info',
        SIGNED: 'success',
        EXPIRED: 'warning',
        TERMINATED: 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        PENDING_SIGN: '待签署',
        SIGNED: '已签署',
        EXPIRED: '已过期',
        TERMINATED: '已终止'
      }
      return statusMap[status] || status
    },

    handleDownload(row) {
      // TODO: Implement download functionality
      console.log('Download:', row)
    }
  }
}
</script>

<style scoped></style>
