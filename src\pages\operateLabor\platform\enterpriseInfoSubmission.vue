<template>
  <div
    class="enterpriseInfoSubmission"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="lite" style="display: flex; align-items: center">
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleGenerate"> 生成报送表 </el-button>
    </div>
    <div class="table-container">
      <el-table
        v-loading="loading"
        size="small"
        :data="data"
        class="responsive-table"
        :header-cell-style="{
          'font-size': '12px',
          'font-weight': '400',
          color: '#777c94',
          background: 'var(--o-primary-bg-color)'
        }"
      >
        <el-table-column
          prop="id"
          label="企业信息报送表ID"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.id) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="supplierCorporationName"
          label="作业主体名称"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.supplierCorporationName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          min-width="180"
          label="生成时间"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="statusDesc"
          min-width="120"
          label="生成状态"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span :class="['status-tag', getStatusClass(scope.row.status)]">
              {{ getStatusText(scope.row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDownload(scope.row)"
              :disabled="scope.row.status === 'GENERATING'"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div style="text-align: right; padding: 20px 0; flex: 0 0 auto">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
        :page-size="conditions.limit"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 生成报送表对话框 -->
    <el-dialog
      title="生成报送表"
      :visible.sync="generateDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="generateForm"
        :rules="generateRules"
        ref="generateForm"
        label-width="100px"
      >
        <el-form-item label="作业主体" prop="supplierCorporationId" required>
          <el-select
            filterable
            v-model="generateForm.supplierCorporationId"
            placeholder="请选择所属作业主体"
            style="width: 300px"
            clearable
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGenerate" :loading="generating"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import { getToken } from '../../../helpers/token'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      supplierOptions: [],
      // 生成报送表对话框相关
      generateDialogVisible: false,
      generating: false,
      generateForm: {
        supplierCorporationId: ''
      },
      generateRules: {
        supplierCorporationId: [
          {
            required: true,
            message: '请选择作业主体',
            trigger: ['change', 'blur']
          }
        ]
      }
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.getList()
  },
  methods: {
    // 格式化文本显示
    formatText(value) {
      return value || '-'
    },
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载业务主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      }
      this.getList()
    },

    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      const [err, r] = await client.infoEnterpriseList({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleGenerate() {
      // 重置表单
      this.generateForm = {
        supplierCorporationId: ''
      }
      this.$nextTick(() => {
        if (this.$refs.generateForm) {
          this.$refs.generateForm.clearValidate()
        }
      })
      this.generateDialogVisible = true
    },
    async confirmGenerate() {
      // 验证表单
      const valid = await this.$refs.generateForm.validate().catch(() => false)
      if (!valid) {
        return
      }

      this.generating = true

      try {
        const requestData = {
          id: 0,
          supplierCorporationId: this.generateForm.supplierCorporationId,
          status: '',
          fileId: ''
        }

        const [err, response] = await client.addInfoEnterprise({
          body: requestData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('报送表生成成功')
          this.generateDialogVisible = false
          // 重新查询列表
          await this.getList()
        } else {
          this.$message.error(response.message || '生成失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.generating = false
      }
    },
    async handleDownload(row) {
      try {
        // 使用fetch API携带token下载文件
        const token = `Bearer ${getToken()}`
        const response = await fetch(
          `${window.env?.apiPath}/api/supplier/infoenterprise/download`,
          {
            method: 'POST',
            headers: {
              Authorization: token,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id: row.id
            })
          }
        )

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `企业信息报送表_${row.id}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('报送表导出成功')
      } catch (error) {
        console.error('导出报送表失败：', error)
        this.$message.error('导出报送表失败')
      }
    },
    getStatusText(status) {
      const statusMap = {
        GENERATING: '生成中',
        GENERATED: '已生成'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      const classMap = {
        GENERATING: 'status-generating',
        GENERATED: 'status-generated'
      }
      return classMap[status] || 'status-default'
    }
  }
}
</script>

<style scoped>
/* 表格容器 */
.table-container {
  flex: 1 1 auto;
  min-height: 0;
  overflow: hidden;
}

.responsive-table {
  width: 100%;
  height: 100%;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-generating {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-generated {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-default {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}
</style>
