<template>
  <div v-loading="loading">
    <!-- 申报表基本信息 -->
    <div style="background: white; padding: 20px; margin-bottom: 20px;">
      <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
        <h1 style="font-size: 25px; font-weight: 600; color: #262626; margin: 0; line-height: 1.2;">
          {{ taxData.supplierCorporationName }}
        </h1>
        <el-button type="primary" @click="handleExport">导出申报表</el-button>
      </div>

      <!-- 基本信息展示 -->
      <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; font-size: 14px;">
        <div>
          <span style="color: #8c8c8c;">税款所属期：</span>
          <span>{{ formatText(taxData.taxPaymentPeriod) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">个税申报月：</span>
          <span>{{ formatText(taxData.incomeTaxMonth) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">总人数：</span>
          <span>{{ formatText(taxData.taxpayersCount) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">增值税总额：</span>
          <span>{{ formatAmount(taxData.totalVatAmount) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">附加税总额：</span>
          <span>{{ formatAmount(taxData.totalSurtaxAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- 增值税申报详情列表 -->
    <el-tabs v-model="activeTab" type="card" style="padding: 10px">
      <el-tab-pane label="申报详情" name="detail">
        <div style="padding: 5px;">
          <el-table
            v-loading="detailLoading"
            :data="detailData"
            style="width: 100%; margin-top: 10px;"
            :header-cell-style="{
              'font-size': '12px',
              'font-weight': '400',
              color: '#777c94',
              background: 'var(--o-primary-bg-color)'
            }"
          >
            <!-- 从业人员信息 -->
            <el-table-column label="从业人员信息" align="center">
              <el-table-column
                prop="name"
                label="姓名"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.name) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="certificateType"
                label="证件类型"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ getCertificateTypeText(scope.row.certificateType) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="idCard"
                label="证件号码"
                width="180"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.idCard) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="countryRegion"
                label="国家地区"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.countryRegion) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="address"
                label="地址"
                width="150"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.address) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="userName"
                label="用户名称"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.userName) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="userUniqueCode"
                label="用户唯一标识"
                width="150"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.userUniqueCode) }}
                </template>
              </el-table-column>
            </el-table-column>

            <!-- 增值税 -->
            <el-table-column label="增值税" align="center">
              <el-table-column
                prop="taxBasis"
                label="计税依据(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.taxBasis) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="taxItem"
                label="税目"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.taxItem) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="taxRate"
                label="税率(%)"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.taxRate) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="vatAmount"
                label="增值税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.vatAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="vatExemptionCode"
                label="减免性质代码"
                width="130"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.vatExemptionCode) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="vatExemptionAmount"
                label="减免税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.vatExemptionAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="vatPayable"
                label="应纳税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.vatPayable) }}
                </template>
              </el-table-column>
            </el-table-column>

            <!-- 城市维护建设税 -->
            <el-table-column label="城市维护建设税" align="center">
              <el-table-column
                prop="urbanTaxRate"
                label="税率(%)"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.urbanTaxRate) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="urbanTaxAmount"
                label="税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.urbanTaxAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="urbanExemptionCode"
                label="减免性质代码"
                width="130"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.urbanExemptionCode) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="urbanExemptionAmount"
                label="减免税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.urbanExemptionAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="urbanTaxPayable"
                label="应纳税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.urbanTaxPayable) }}
                </template>
              </el-table-column>
            </el-table-column>

            <!-- 教育附加 -->
            <el-table-column label="教育附加" align="center">
              <el-table-column
                prop="eduTaxRate"
                label="税率(%)"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.eduTaxRate) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="eduTaxAmount"
                label="税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.eduTaxAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="eduExemptionCode"
                label="减免性质代码"
                width="130"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.eduExemptionCode) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="eduExemptionAmount"
                label="减免税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.eduExemptionAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="eduTaxPayable"
                label="应纳税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.eduTaxPayable) }}
                </template>
              </el-table-column>
            </el-table-column>

            <!-- 地方教育附加 -->
            <el-table-column label="地方教育附加" align="center">
              <el-table-column
                prop="localEduTaxRate"
                label="税率(%)"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.localEduTaxRate) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="localEduTaxAmount"
                label="税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.localEduTaxAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="localEduExemptionCode"
                label="减免性质代码"
                width="130"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatText(scope.row.localEduExemptionCode) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="localEduExemptionAmount"
                label="减免税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.localEduExemptionAmount) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="localEduTaxPayable"
                label="应纳税额(元)"
                width="120"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.localEduTaxPayable) }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="detailTotal > 0"
            @current-change="handleDetailCurrentChange"
            :current-page="detailConditions.offset / detailConditions.limit + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="detailConditions.limit"
            layout="total, prev, pager, next"
            :total="detailTotal"
            style="text-align: right; margin-top: 10px"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import { getToken } from '../../../helpers/token'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      activeTab: 'detail',
      loading: true,
      detailLoading: false,
      taxData: {
        id: '',
        supplierCorporationName: '',
        taxPaymentPeriod: '',
        incomeTaxMonth: '',
        taxpayersCount: '',
        currentIncome: '',
        createTime: '',
        modifyTime: '',
        supplierId: '',
        totalVatAmount: 0,
        totalSurtaxAmount: 0
      },
      detailData: [],
      allDetailData: [], // 存储所有详情数据
      detailTotal: 0,
      detailConditions: {
        offset: 0,
        limit: 10
      }
    }
  },
  async created() {
    const id = this.$route.params.id
    if (id) {
      await this.loadTaxDetail(id)
    }
  },
  methods: {
    async loadTaxDetail(id) {
      this.loading = true
      try {
        const [err, response] = await client.queryValueAddedTax({
          body: { id: parseInt(id) }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success && response.data) {
          this.taxData = response.data
          this.allDetailData = response.data.details || []
          this.detailTotal = this.allDetailData.length
          this.updateDetailData()
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },
    handleDetailCurrentChange(page) {
      this.detailConditions.offset = (page - 1) * this.detailConditions.limit
      this.updateDetailData()
    },
    updateDetailData() {
      const start = this.detailConditions.offset
      const end = start + this.detailConditions.limit
      this.detailData = this.allDetailData.slice(start, end)
    },
    async handleExport() {
      try {
        // 使用fetch API携带token下载文件
        const token = `Bearer ${getToken()}`
        const response = await fetch(`${window.env?.apiPath}/api/supplier/valueaddedtax/download/declarationRecord`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: this.taxData.id
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.taxData.taxPaymentPeriod}_增值税申报表.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('申报表导出成功')
      } catch (error) {
        console.error('导出申报表失败：', error)
        this.$message.error('导出申报表失败')
      }
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '-'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatText(text) {
      if (text === null || text === undefined || text === '') {
        return '-'
      }
      return text
    },
    getCertificateTypeText(type) {
      const typeMap = {
        'ID_CARD': '身份证',
        'PASSPORT': '护照',
        'HK_MACAO_PASS': '港澳通行证',
        'TAIWAN_PASS': '台湾通行证'
      }
      return typeMap[type] || '-'
    }
  }
}
</script>

<style scoped>
</style>
