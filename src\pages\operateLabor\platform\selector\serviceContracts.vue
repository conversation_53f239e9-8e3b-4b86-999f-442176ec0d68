<template>
  <div class="serviceContractsSelector">
    <el-select
      v-model="selectedContractId"
      filterable
      clearable
      remote
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      style="width: 100%"
      :loading="loading"
      value-key="id"
      @change="handleChange"
    >
      <el-option
        v-for="item in contracts"
        :key="item.id"
        :label="item.name + ' | ' + item.customerName"
        :value="item.id"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'ServiceContractsSelector',
  props: {
    value: {
      type: [String, Number],
      default: null
    },
    customerId: {
      type: [String, Number],
      default: null
    },
    corporationId: {
      type: [String, Number],
      default: null
    },
    placeholder: {
      type: String,
      default: '请选择服务合同'
    }
  },
  data() {
    return {
      loading: false,
      contracts: [],
      selectedContractId: null
    }
  },
  async created() {
    this.fetchContracts()
    // if (this.value) {
    //   // Handle initial value
    //   const contractId = this.value
    //   await this.fetchContractById(contractId)
    //   this.selectedContractId = contractId
    // }
  },
  methods: {
    reset() {
      this.selectedContractId = null
    },
    remoteMethod(query) {
      this.fetchContracts(query)
    },
    async fetchContracts(name = '') {
      this.loading = true
      const [err, r] = await client.supplierListContract({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            name: name
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.contracts = r.data.list || []
    },
    handleChange(value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style scoped>
.serviceContractsSelector {
  width: 100%;
}
</style>
