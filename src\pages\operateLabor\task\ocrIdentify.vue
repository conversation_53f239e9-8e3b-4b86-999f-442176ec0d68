<template>
  <div class="id-page">
    <div class="header">
      <h2>身份信息确认</h2>
      <p class="subtitle">请确认您的身份信息</p>
    </div>

    <div class="form-section">
      <van-form>
        <van-cell-group>
          <van-field
            v-model="formData.expectedName"
            name="expectedName"
            label="姓名"
            placeholder="请输入姓名"
            :rules="[{ required: true, message: '请输入姓名' }]"
            clearable
          />

          <van-field
            v-model="formData.expectedIdNo"
            name="expectedIdNo"
            label="身份证号"
            placeholder="请输入身份证号码"
            :rules="[
              { required: true, message: '请输入身份证号码' },
              { validator: validateIdNumber, message: '身份证号码格式不正确' }
            ]"
            clearable
          />
        </van-cell-group>
      </van-form>
      <div class="button-section">
        <Button
          class="next-btn"
          type="primary"
          :disabled="!canProceed"
          native-type="submit"
          block
          @click="handleNext"
        >
          下一步
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Form,
  Field,
  CellGroup,
  Button,
  Popup,
  Picker,
  DatetimePicker,
  Toast
} from 'vant'

export default {
  name: 'TaskId',

  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [CellGroup.name]: CellGroup,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [DatetimePicker.name]: DatetimePicker
  },

  data() {
    return {
      formData: {
        expectedName: '',
        expectedIdNo: '',
      },
      showGenderPicker: false,
      showBirthDatePicker: false,
      showValidPeriodPicker: false,
      showValidPeriodEndPicker: false,
      birthDateValue: new Date(),
      validPeriodStartValue: new Date(),
      validPeriodEndValue: new Date(),
      genderColumns: [
        { text: '男', value: 'male' },
        { text: '女', value: 'female' }
      ],
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      maxValidDate: new Date(2050, 11, 31)
    }
  },

  computed: {
    canProceed() {
      return this.formData.expectedName && this.formData.expectedIdNo
    }
  },

  created() {
    const { frontImage, backImage } = this.$route.query
  },

  methods: {
    validateIdNumber(value) {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      return reg.test(value)
    },

    onGenderConfirm(value) {
      this.formData.gender = value.text
      this.showGenderPicker = false
    },

    onBirthDateConfirm(value) {
      this.formData.birthDate = this.formatDate(value)
      this.showBirthDatePicker = false
    },

    onValidPeriodStartConfirm(value) {
      this.validPeriodStartValue = value
      this.showValidPeriodPicker = false
      this.showValidPeriodEndPicker = true
    },

    onValidPeriodEndConfirm(value) {
      this.validPeriodEndValue = value
      this.formData.validPeriod = [
        this.formatDate(this.validPeriodStartValue),
        this.formatDate(this.validPeriodEndValue)
      ]
      this.showValidPeriodEndPicker = false
    },

    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    async handleNext() {
      if (!this.canProceed) {
        Toast('请完善表单信息')
        return
      }

      const [err, r] = await client.describeFile({
        body: { id: this.value }
      })

      // 跳转到人脸认证页面
      this.$router.push({
        path: '/task/faceAuth',
        query: {
          expectedName: this.formData.expectedName,
          expectedIdNo: this.formData.expectedIdNo
        }
      })
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.id-page {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 0;
}

.header {
  text-align: center;
  padding: 20px 20px 30px 20px;
  background: white;
  margin-bottom: 10px;
}

.header h2 {
  font-size: 20px;
  color: #323233;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.form-section {
  background: white;
}

.button-section {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.next-btn {
  width: 100%;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

/* 自定义Vant组件样式 */
:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 80px;
  color: #323233;
  font-weight: 500;
}

:deep(.van-field__control) {
  color: #323233;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}

:deep(.van-button--primary) {
  background: #4285f4;
  border-color: #4285f4;
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-button--primary:disabled) {
  background: #c8c9cc;
  border-color: #c8c9cc;
}

:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

:deep(.van-picker__toolbar) {
  padding: 16px;
}

:deep(.van-picker__title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-datetime-picker__toolbar) {
  padding: 16px;
}

:deep(.van-datetime-picker__title) {
  font-size: 16px;
  font-weight: 600;
}
</style>
