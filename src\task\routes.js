import Home from './app.vue'
import Login from 'kit/pages/operateLabor/task/login.vue'
import OcrIdentify from 'kit/pages/operateLabor/task/ocrIdentify.vue'
import FaceAuth from 'kit/pages/operateLabor/task/faceAuth.vue'
import Ocr from 'kit/pages/operateLabor/task/ocr.vue'

const routes = [
  {
    path: '/',
    component: Home
  },
  {
    path: '/login',
    component: Login
  },
  {
    path: '/ocr',
    component: Ocr
  },
  {
    path: '/ocrIdentify',
    component: OcrIdentify
  },
  {
    path: '/faceAuth',
    component: FaceAuth
  }
]

export default routes
