<template>
  <div v-loading="loading">
    <el-form :model="form" :rules="rules" ref="form" label-width="180px" style="width: 800px;margin-left: 50px;">

      <Title title="基本信息" />
      <el-form-item label="客户名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入客户名称"></el-input>
      </el-form-item>

      <el-form-item label="营业执照照片" prop="businessLicenseImage">
        <div class="tip">
            请上传加盖公章的营业执照扫件或复印件
            <el-button type="text" @click="showExample('business')">
              查看示例
            </el-button>
        </div>
        <div style="display: inline-flex; flex-direction: column; align-items: center;">
          <ImagerUploader v-model="form.businessLicenseImage" :max="1" :width="200"
            :height="280" accept=".jpg,.jpeg,.png" name="营业执照" />
        </div>
      </el-form-item>

      <el-form-item label="公司名称" prop="shortName">
        <el-input v-model="form.shortName" placeholder="请输入公司名称"></el-input>
      </el-form-item>

      <el-form-item label="统一社会信用代码" prop="socialCreditCode">
        <el-input v-model="form.socialCreditCode" placeholder="请输入统一社会信用代码"></el-input>
      </el-form-item>

      <el-form-item label="公司注册地址" prop="registerAddress">
        <el-input v-model="form.registerAddress" placeholder="请输入公司注册地址"></el-input>
      </el-form-item>

      <Title title="法人信息" />
      <el-form-item label="法人身份证" prop="certificateFrontImage">
        <div class="tip">
            请上传加盖公章的法人身份证扫件或复印件
            <el-button type="text" @click="showExample('certificate')">
              查看示例
            </el-button>
        </div>
        <div style="display: inline-flex; flex-direction: column; align-items: center;">
          <ImagerUploader v-model="form.certificateFrontImage" :max="1" :width="230" :height="170" name="上传人像面" />
          <div style="margin-top: 8px; font-size: 12px; color: #999;">人像面</div>
        </div>
      </el-form-item>

      <el-form-item label="" prop="certificateBackImage">
        <div style="display: inline-flex; flex-direction: column; align-items: center;">
          <ImagerUploader v-model="form.certificateBackImage" :max="1" :width="230" :height="170" name="上传国徽面" />
          <div style="margin-top: 8px; font-size: 12px; color: #999;">国徽面</div>
        </div>
      </el-form-item>

      <el-form-item label="法人姓名" prop="representativeName">
        <el-input v-model="form.representativeName" placeholder="请输入法人姓名"></el-input>
      </el-form-item>

      <el-form-item label="法人证件类型" prop="certificateType">
        <el-select v-model="form.certificateType" placeholder="请选择证件类型" style="width: 100%">
          <el-option label="身份证" value="ID_CARD"></el-option>
          <el-option label="护照" value="PASSPORT"></el-option>
          <el-option label="港澳通行证" value="HK_MACAO_PASS"></el-option>
          <el-option label="台湾通行证" value="TAIWAN_PASS"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="法人证件号" prop="certificateNo">
        <el-input v-model="form.certificateNo" placeholder="请输入法人证件号"></el-input>
      </el-form-item>

      <Title title="联系人信息" />
      <el-form-item label="联系人姓名" prop="contactName">
        <el-input v-model="form.contactName" placeholder="请输入联系人姓名"></el-input>
      </el-form-item>
      <el-form-item label="联系人手机" prop="contactMobile">
        <el-input v-model="form.contactMobile" placeholder="请输入联系人手机"></el-input>
      </el-form-item>

      <Title title="操作角色" />
      <el-form-item label="操作角色" prop="roleIds">
        <RolesSelector v-model="form.roleIds" style="width: 620px" />
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="submitting">保存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import ImagerUploader from './uploader/image.vue'
import RolesSelector from './selector/roles.vue'

const client = makeClient()

export default {
  components: { Title, ImagerUploader,RolesSelector },
  data() {
    return {
      loading:true,
      customerId: null,
      submitting: false,
      conditions: {
        offset: 0,
        limit: 1000,
        sorts: [],
        withTotal: false,
        withDisabled: false,
        withDeleted: false,
        filters: {
          name: ''
        }
      },
      // 操作角色选项列表
      roleOptions: [],

      form: {
        businessLicenseImage: '',
        name: '',
        shortName: '',
        socialCreditCode: '',
        registerAddress: '',
        certificateFrontImage: '',
        certificateBackImage: '',
        representativeName: '',
        certificateType: '',
        certificateNo: '',
        contactName: '',
        contactMobile: '',
        roleIds: [],
      },

      rules: {
        name: [
          { required: true, message: '请输入客户名称', trigger: 'blur' }
        ],
        shortName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' }
        ],
        socialCreditCode: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur'
          },
          {
            pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
            message: '请输入正确的统一社会信用代码',
            trigger: 'blur'
          }
        ],
        registerAddress: [
          { required: true, message: '请输入公司注册地址', trigger: 'blur' }
        ],
        representativeName: [
          { required: true, message: '请输入法人姓名', trigger: 'blur' }
        ],
        certificateType: [
          { required: true, message: '请选择证件类型', trigger: 'change' }
        ],
        certificateNo: [
          { required: true, message: '请输入法人证件号', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' }
        ],
        contactMobile: [
          { required: true, message: '请输入联系人手机', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        businessLicenseImage: [
          {
            required: true,
            message: '请上传营业执照照片',
            trigger: 'change',
            validator: (_, value, callback) => {
              // value可能是字符串ID或对象
              const fileId = typeof value === 'object' && value ? value.id : value
              if (!fileId) {
                callback(new Error('请上传营业执照照片'))
              } else {
                callback()
              }
            }
          }
        ],
        certificateFrontImage: [
          {
            required: true,
            message: '请上传法人身份照片',
            trigger: 'change',
            validator: (_, value, callback) => {
              const fileId = typeof value === 'object' && value ? value.id : value
              if (!fileId) {
                callback(new Error('请上传法人身份证正面照片'))
              } else {
                callback()
              }
            }
          }
        ],
        certificateBackImage: [
          {
            required: true,
            message: '请上传法人身份证背面照片',
            trigger: 'change',
            validator: (_, value, callback) => {
              const fileId = typeof value === 'object' && value ? value.id : value
              if (!fileId) {
                callback(new Error('请上传法人身份证背面照片'))
              } else {
                callback()
              }
            }
          }
        ],
      }
    }
  },
  watch: {
    // 监听营业执照文件变化，确保存储的是文件ID
    'form.businessLicenseImage': {
      handler(newVal) {
        console.log('businessLicenseImage changed:', newVal, typeof newVal)
        // 如果是对象格式，提取ID
        if (typeof newVal === 'object' && newVal && newVal.id) {
          // 避免无限循环：只有当当前值不是ID字符串时才更新
          if (this.form.businessLicenseImage !== newVal.id) {
            this.form.businessLicenseImage = newVal.id
            return // 避免重复触发验证
          }
        }
        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.validateField('businessLicenseImage')
        })
      },
      deep: true
    },
    // 监听身份证正面文件变化
    'form.certificateFrontImage': {
      handler(newVal) {
        console.log('certificateFrontImage changed:', newVal, typeof newVal)
        if (typeof newVal === 'object' && newVal && newVal.id) {
          if (this.form.certificateFrontImage !== newVal.id) {
            this.form.certificateFrontImage = newVal.id
            return
          }
        }
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.validateField('certificateFrontImage')
        })
      },
      deep: true
    },
    // 监听身份证背面文件变化
    'form.certificateBackImage': {
      handler(newVal) {
        console.log('certificateBackImage changed:', newVal, typeof newVal)
        if (typeof newVal === 'object' && newVal && newVal.id) {
          if (this.form.certificateBackImage !== newVal.id) {
            this.form.certificateBackImage = newVal.id
            return
          }
        }
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.validateField('certificateBackImage')
        })
      },
      deep: true
    }
  },
  async created() {
    // 获取路由参数中的客户ID
    this.customerId = this.$route.params.id
    
    // 加载数据
    await this.loadCustomerData()
  },

  methods: {
    // 加载客户数据
    async loadCustomerData() {
      try {
        const [err, response] = await client.queryCustomer({
          body: { id: this.customerId }
        })
        console.log('response', response)

        if (response.success) {
          const data = response.data || {}
          // 将API返回的数据映射到表单
          this.form = {
            businessLicenseImage: data.businessLicenseImage || '',
            name: data.name || '',
            shortName: data.shortName || '',
            socialCreditCode: data.socialCreditCode || '',
            registerAddress: data.registeredAddress || data.address || '',
            certificateFrontImage: data.certificateFrontImage || '',
            certificateBackImage: data.certificateBackImage || '',
            representativeName: data.representativeName || '',
            certificateType: data.certificateType || '',
            certificateNo: data.certificateNo || '',
            contactName: data.enterpriseContacts || '',
            contactMobile: data.enterpriseContactPhone || '',
            roleIds: data.roleIds || [],
          }
        } else {
          this.$message.error(response.message || '获取客户信息失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },

    // 提交表单
    async onSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        // 调用更新接口
        const [err, response] = await client.updateCustomer({
          body: {
            id: this.customerId,
            ...this.form
          }
        })

        if (response.success) {
          this.$message.success('客户信息更新成功')
          // 返回列表页面
          this.$router.push('/supplierCustomers')
        } else {
          this.$message.error(response.message || '更新失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    },
    showExample(type) {
      console.log('显示示例:', type)
    },
    // 取消编辑
    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}
</style>
